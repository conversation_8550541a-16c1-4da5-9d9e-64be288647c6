import { getData, getDataType } from "@/components/data/useData";
import { DeviceIcons } from "@/components/icons/DeviceIcons";
import { ConnectableWithIsUsed } from "@/components/layout-graph/controllers/useConnectableDevices";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { MultiSelect } from "@/components/ui/multi-select";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { TooltipDropdownMenu } from "@/components/ui/tooltip-dropdown-menu";
import { diff } from "@/lib/array";
import { clamp } from "@/lib/math";
import {
  ControllableNoHybrid,
  DeviceControlSettings,
  FanControlSettings,
  filterNodes,
  isDimmableLightType,
  LayoutContainerNode,
  MomentaryCanBoViaId,
  OccupancySensorViaId,
  PluginDimmerAction,
  PluginDimmerViaId,
  ReedSwitchSensorViaId,
  RoomDimmerAction,
  RoomDimmerViaId,
  RoomSwitchAction,
  RoomSwitchViaId,
  SceneInput,
  ShadesControlSettings,
  ThermostatControlSettings,
  ThermostatFanSpeed,
  ThermostatMode,
  ToggleCanBoViaId,
} from "@somo/shared";
import {
  CircleAlertIcon,
  PlusIcon,
  SearchIcon,
  Trash2Icon,
} from "lucide-react";
import { useState } from "react";
import { match } from "ts-pattern";
import { defaultNameForNodeType } from "../NodeList";
import { useReactFlowContext } from "../ReactFlowContext";
import {
  addInput,
  areInputsEqual,
  removeInput,
  wouldBeCircular,
} from "../scene/sceneOperations";
import { SettingsLabel, SettingsRow } from "./SettingsGroup";
import { SettingsInput } from "./SettingsInput";

// Common input component for action settings
function ActionInput({
  label,
  value,
  unit,
  onEndEdit,
  className = "w-auto",
  inputClassName,
  disabled = false,
  parseAs = "float",
  min = 0,
  max = 100,
}: {
  label: string;
  value: string;
  unit?: string;
  onEndEdit: (value: number) => void;
  className?: string;
  inputClassName?: string;
  disabled?: boolean;
  parseAs?: "float" | "int";
  min?: number;
  max?: number;
}) {
  const handleEndEdit = (value: string) => {
    const numValue =
      parseAs === "int" ? parseInt(value, 10) : parseFloat(value);
    if (isNaN(numValue)) {
      return;
    }
    const clampedValue =
      parseAs === "int" ? clamp(numValue, { min, max }) : numValue;
    onEndEdit(clampedValue);
  };

  if (!inputClassName) {
    inputClassName = unit ? "w-[28px] text-right" : "w-[40px] text-center";
  }

  return (
    <SettingsInput
      label={label}
      value={value}
      unit={unit}
      disabled={disabled}
      className={className}
      inputClassName={inputClassName}
      onEndEdit={handleEndEdit}
    />
  );
}

// Common input groups for different action types
function DimSpeedInput({
  action,
  disabled,
  onChange,
}: {
  action: DeviceControlSettings;
  disabled: boolean;
  onChange: (value: number) => void;
}) {
  return (
    <ActionInput
      label="Dim Speed"
      unit="s"
      value={`${action.dimSpeed ?? 0}`}
      disabled={disabled}
      onEndEdit={onChange}
      inputClassName="w-[25px] text-right"
    />
  );
}

function DelayInput({
  action,
  disabled,
  onChange,
}: {
  action: DeviceControlSettings;
  disabled: boolean;
  onChange: (value: number) => void;
}) {
  return (
    <ActionInput
      label="Delay"
      unit="ms"
      value={`${action.delay ?? 0}`}
      disabled={disabled}
      inputClassName="w-[40px] text-right"
      parseAs="int"
      min={0}
      max={1_800_000}
      onEndEdit={onChange}
    />
  );
}

function ValueInput({
  label,
  value,
  disabled,
  onChange,
}: {
  label: string;
  value: number;
  disabled: boolean;
  onChange: (value: number) => void;
}) {
  return (
    <ActionInput
      label={label}
      unit="%"
      value={`${value ?? 0}`}
      disabled={disabled}
      onEndEdit={onChange}
    />
  );
}

export type ActionSettingsType = "lighting" | "thermostat" | "nothing";
export type ActionType =
  | RoomDimmerAction
  | RoomSwitchAction
  | PluginDimmerAction
  | MomentaryCanBoViaId
  | ToggleCanBoViaId
  | OccupancySensorViaId
  | RoomDimmerViaId
  | RoomSwitchViaId
  | PluginDimmerViaId
  | ReedSwitchSensorViaId;

type BaseProps = {
  node: LayoutContainerNode;
  label: string;
  triggerId: string;
  devices: ConnectableWithIsUsed[];
  actionType: ActionType;
  missingDeviceText?: string;
  onAddAction: (deviceId: string) => void;
  onDelete: (id: string) => void;
  onDeviceChange: (id: string, value: string) => void;
};
type Props = BaseProps &
  (
    | {
        actions: DeviceControlSettings[];
        type: "lighting";
        onDelayChange: (id: string, value: number) => void;
        onDimSpeedChange: (id: string, value: number) => void;
        onOnValueChange: (id: string, value: number) => void;
        onOffValueChange: (id: string, value: number) => void;
        onTargetValueChange: (id: string, value: number) => void;
      }
    | {
        actions: ThermostatControlSettings[];
        type: "thermostat";
        onSetPointChange: (id: string, value: number) => void;
        onModeChange: (id: string, value: ThermostatMode) => void;
        onFanSpeedChange: (id: string, value: ThermostatFanSpeed) => void;
      }
    | {
        actions: (ShadesControlSettings | FanControlSettings)[];
        type: "nothing";
      }
  );

export function ActionSettingsWithHeader(props: Props) {
  const { readOnly, nodes, yDoc } = useReactFlowContext();

  const { id, type, data } = props.node;
  const firstUnusedDeviceId = props.devices.find((d) => !d.isUsed)?.id;
  const defaultDeviceId = firstUnusedDeviceId ?? props.devices[0]?.id;

  const scenes = filterNodes(nodes, "scene")
    .filter((scene) => id !== scene.id)
    .map((scene) => {
      const sceneData = getData(yDoc, scene.data.dataId, "scene");
      return {
        value: scene.data.dataId,
        label: sceneData?.title || "Scene",
        data: scene.data,
        inputs: sceneData?.inputs,
        disabled:
          type === "scene" &&
          wouldBeCircular(yDoc, scene.data.dataId, data.dataId),
      };
    });

  const sceneInput = {
    dataId: data.dataId,
    triggerId: props.triggerId,
    actionName: props.actionType,
  } satisfies SceneInput;
  const selectedScenes = scenes
    .filter(({ inputs }) => inputs?.find((i) => areInputsEqual(i, sceneInput)))
    .map((scene) => scene.value);

  return (
    <div className="space-y-4">
      <SettingsRow className="flex flex-row justify-between items-center -mb-2">
        <SettingsLabel>{props.label}</SettingsLabel>

        {!readOnly && (
          <Tooltip>
            <TooltipTrigger asChild>
              <TooltipDropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-6 data-[state=open]:bg-gray-100"
                  >
                    <PlusIcon className="size-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="mr-5">
                  <DropdownMenuLabel>Add action</DropdownMenuLabel>
                  {defaultDeviceId ? (
                    <DropdownMenuItem
                      onClick={() => props.onAddAction(defaultDeviceId)}
                    >
                      Add action
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem disabled>
                      <div className="flex items-center text-red-600">
                        <CircleAlertIcon className="mr-2 size-4" />
                        {props.missingDeviceText ?? "Create a device first"}
                      </div>
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </TooltipDropdownMenu>
            </TooltipTrigger>
            <TooltipContent>Add action</TooltipContent>
          </Tooltip>
        )}
      </SettingsRow>

      {scenes.length > 0 && (
        <MultiSelect
          className="w-full"
          placeholder="Select scenes"
          closeOnSelect
          hideSelectAll
          autoSize={false}
          variant="secondary"
          disabled={readOnly}
          maxCount={5}
          searchable={scenes.length > 5}
          options={scenes}
          defaultValue={selectedScenes}
          onValueChange={(newSelectedScenes) => {
            const { added, removed } = diff({
              before: selectedScenes,
              after: newSelectedScenes,
            });

            removed.forEach((sceneId) => {
              removeInput({
                yDoc,
                sceneId,
                input: sceneInput,
              });
            });
            added.forEach((sceneId) => {
              addInput({
                yDoc,
                sceneId,
                input: sceneInput,
              });
            });
          }}
          emptyIndicator={
            <div className="flex flex-col items-center p-4">
              <SearchIcon className="h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-muted-foreground">No scenes found</p>
              <p className="text-xs text-muted-foreground">
                Try a different search term
              </p>
            </div>
          }
        />
      )}

      {props.actions.length === 0 && (
        <div className="text-gray-500 text-xs">No actions defined.</div>
      )}

      <SettingsRow>
        {match(props)
          .with({ type: "nothing" }, ({ actions }) =>
            actions.map((action) => (
              <div className="mx-3" key={action.id}>
                <ActionSettings
                  type="nothing"
                  action={action}
                  actionType={props.actionType}
                  devices={props.devices}
                  onDelete={() => props.onDelete(action.id)}
                  onDeviceChange={(v) => props.onDeviceChange(action.id, v)}
                />
              </div>
            )),
          )
          .with(
            { type: "thermostat" },
            ({ actions, onSetPointChange, onModeChange, onFanSpeedChange }) =>
              actions.map((action) => (
                <div className="mx-3" key={action.id}>
                  <ActionSettings
                    type="thermostat"
                    action={action}
                    actionType={props.actionType}
                    devices={props.devices}
                    onDelete={() => props.onDelete(action.id)}
                    onDeviceChange={(v) => props.onDeviceChange(action.id, v)}
                    onSetPointChange={(v) => onSetPointChange(action.id, v)}
                    onModeChange={(v) => onModeChange(action.id, v)}
                    onFanSpeedChange={(v) => onFanSpeedChange(action.id, v)}
                  />
                </div>
              )),
          )
          .with(
            { type: "lighting" },
            ({
              actions,
              onDelayChange,
              onDimSpeedChange,
              onOnValueChange,
              onOffValueChange,
              onTargetValueChange,
            }) =>
              actions.map((action) => (
                <div className="mx-3" key={action.id}>
                  <ActionSettings
                    type="lighting"
                    action={action}
                    actionType={props.actionType}
                    devices={props.devices}
                    onDelete={() => props.onDelete(action.id)}
                    onDeviceChange={(v) => props.onDeviceChange(action.id, v)}
                    onDelayChange={(v) => onDelayChange(action.id, v)}
                    onDimSpeedChange={(v) => onDimSpeedChange(action.id, v)}
                    onOnValueChange={(v) => onOnValueChange(action.id, v)}
                    onOffValueChange={(v) => onOffValueChange(action.id, v)}
                    onTargetValueChange={(v) =>
                      onTargetValueChange(action.id, v)
                    }
                  />
                </div>
              )),
          )
          .exhaustive()}
      </SettingsRow>
    </div>
  );
}

type ActionSettingsProps = {
  devices: ConnectableWithIsUsed[];
  actionType: ActionType;
  onDelete: () => void;
  onDeviceChange: (value: string) => void;
} & (
  | {
      action: DeviceControlSettings;
      type: "lighting";
      onDelayChange: (value: number) => void;
      onDimSpeedChange: (value: number) => void;
      onOnValueChange: (value: number) => void;
      onOffValueChange: (value: number) => void;
      onTargetValueChange: (value: number) => void;
    }
  | {
      action: ThermostatControlSettings;
      type: "thermostat";
      onSetPointChange: (value: number) => void;
      onModeChange: (value: ThermostatMode) => void;
      onFanSpeedChange: (value: ThermostatFanSpeed) => void;
    }
  | {
      action: ShadesControlSettings | FanControlSettings;
      type: "nothing";
    }
);

export type SettingsType = "onOffToggle" | "turnOn" | "turnOff" | "targetValue";

function ActionSettings(props: ActionSettingsProps) {
  const { readOnly, yDoc, nodes } = useReactFlowContext();

  const deviceType = getDataType(yDoc, props.action.deviceId);
  if (!deviceType) {
    return null;
  }
  const showDimmableInputs = isDimmableLightType(deviceType);

  return (
    <SettingsRow className="flex flex-col justify-between items-center gap-2 -mx-3">
      <div className="flex flex-row items-center gap-2 w-full">
        <Select
          onValueChange={props.onDeviceChange}
          value={props.action.deviceId}
          disabled={readOnly}
        >
          <SelectTrigger className="h-10 min-h-10 w-full">
            <SelectValue
              placeholder="Select a device"
              className="w-full h-10 min-h-10"
            />
          </SelectTrigger>
          <SelectContent className="w-full">
            {props.devices.map((deviceNode) => {
              const deviceData = getData(yDoc, deviceNode.id, deviceNode.type);
              return deviceData ? (
                <DeviceSelectItem key={deviceNode.id} deviceData={deviceData} />
              ) : null;
            })}
          </SelectContent>
        </Select>
        {!readOnly && <DeleteButton onDelete={props.onDelete} />}
      </div>

      {match(props)
        .with({ type: "nothing" }, () => (
          <div className="flex flex-row items-center gap-2 w-full flex-wrap">
            {/* No additional settings for shades and fan control actions */}
          </div>
        ))
        .with(
          { type: "lighting" },
          ({
            action,
            onDelayChange,
            onDimSpeedChange,
            onOnValueChange,
            onOffValueChange,
            onTargetValueChange,
          }) => {
            // This is a bit of heuristics to determine the action type based on the
            // device's settings
            let defaultActionType = "targetValue";
            if (props.actionType === "onUpClick") {
              if (action.onValue > action.offValue) {
                defaultActionType = "onOffToggle";
              } else if (action.offValue > 50) {
                defaultActionType = "turnOn";
              } else if (action.onValue < 50) {
                defaultActionType = "turnOff";
              }
            }

            const [settingsType, setSettingsType] = useState<SettingsType>(
              defaultActionType as SettingsType,
            );

            return (
              <>
                {settingsType !== "targetValue" && (
                  <div className="flex flex-row items-center gap-2 w-full">
                    <Select
                      disabled={readOnly}
                      value={settingsType}
                      onValueChange={(value: SettingsType) => {
                        setSettingsType(value);
                        if (value === "turnOn") {
                          onOnValueChange(100);
                          onOffValueChange(100);
                        }
                        if (value === "turnOff") {
                          onOnValueChange(0);
                          onOffValueChange(0);
                        }
                        if (value === "onOffToggle") {
                          onOnValueChange(100);
                          onOffValueChange(0);
                        }
                      }}
                    >
                      <SelectTrigger className="h-10 min-h-10 w-full">
                        <SelectValue placeholder="Select action type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="onOffToggle">
                          On/Off Toggle
                        </SelectItem>
                        <SelectItem value="turnOn">Turn On</SelectItem>
                        <SelectItem value="turnOff">Turn Off</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
                <div className="flex flex-row items-center gap-2 w-full flex-wrap">
                  {showDimmableInputs && (
                    <DimSpeedInput
                      action={action}
                      disabled={readOnly}
                      onChange={onDimSpeedChange}
                    />
                  )}
                  {showDimmableInputs &&
                    match(settingsType)
                      .with("onOffToggle", () => (
                        <>
                          <ValueInput
                            label="On Value"
                            value={action.onValue}
                            disabled={readOnly}
                            onChange={onOnValueChange}
                          />
                          <ValueInput
                            label="Off Value"
                            value={action.offValue}
                            disabled={readOnly}
                            onChange={onOffValueChange}
                          />
                        </>
                      ))
                      .with("turnOn", () => (
                        <ValueInput
                          label="Turn On"
                          value={action.onValue}
                          disabled={readOnly}
                          onChange={(value) => {
                            onOnValueChange(value);
                            onOffValueChange(value);
                          }}
                        />
                      ))
                      .with("turnOff", () => (
                        <ValueInput
                          label="Turn Off"
                          value={action.offValue}
                          disabled={readOnly}
                          onChange={(value) => {
                            onOnValueChange(value);
                            onOffValueChange(value);
                          }}
                        />
                      ))
                      .with("targetValue", () => (
                        <ValueInput
                          label="Target Value"
                          value={action.targetValue}
                          disabled={readOnly}
                          onChange={onTargetValueChange}
                        />
                      ))
                      .exhaustive()}
                  <DelayInput
                    action={action}
                    disabled={readOnly}
                    onChange={onDelayChange}
                  />
                </div>
              </>
            );
          },
        )
        .with(
          { type: "thermostat" },
          ({ action, onSetPointChange, onModeChange, onFanSpeedChange }) => {
            const selectedDevice = props.devices.find(
              (device) => device.id === action.deviceId,
            );
            const thermostat = match(selectedDevice)
              .with(
                { type: "roomThermostatControllable" },
                ({ controllerId }) =>
                  filterNodes(nodes, "roomThermostatContainer").find(
                    (n) => n.data.dataId === controllerId,
                  ),
              )
              .otherwise(() => null);
            const irController = match(selectedDevice)
              .with({ type: "irHvac" }, ({ controllerId }) =>
                filterNodes(nodes, "irHvacCtrlContainer").find(
                  (n) => n.data.dataId === controllerId,
                ),
              )
              .otherwise(() => null);

            const thermostatData =
              thermostat &&
              getData(yDoc, thermostat.data.dataId, "roomThermostat");

            const irControllerData =
              irController &&
              getData(yDoc, irController.data.dataId, "irHvacCtrl");

            const temperatureUnit =
              thermostatData?.temperatureUnit ??
              irControllerData?.temperatureUnit ??
              "C";
            const minTemp =
              thermostatData?.minTemp ?? irControllerData?.minTemp ?? 5;
            const maxTemp =
              thermostatData?.maxTemp ?? irControllerData?.maxTemp ?? 35;
            const allowedModes = thermostatData?.allowedModes ??
              irControllerData?.allowedModes ?? ["heat", "cool", "fan", "auto"];

            const allowedFanSpeeds =
              // thermostatData?.allowedFanSpeeds ??
              irControllerData?.allowedFanSpeeds ?? [
                "low",
                "medium",
                "high",
                "auto",
              ];
            return (
              <div className="flex flex-row items-center gap-2 w-full flex-wrap">
                <SettingsInput
                  label={`Setpoint (°${temperatureUnit})`}
                  value={String(action.setpoint)}
                  className="w-auto"
                  inputClassName="w-[50px] text-center"
                  disabled={readOnly}
                  onEndEdit={(value) => {
                    const setpoint = parseFloat(value);
                    if (isNaN(setpoint)) {
                      return;
                    }

                    const clampedSetpoint = clamp(setpoint, {
                      min: minTemp ?? 5,
                      max: maxTemp ?? 35,
                    });
                    onSetPointChange(clampedSetpoint);
                  }}
                />

                <div className="flex flex-row gap-2 items-center">
                  <Label
                    htmlFor="mode"
                    className="text-xs text-gray-500 flex-grow"
                  >
                    Mode
                  </Label>
                  <Select
                    value={action.mode}
                    onValueChange={(value) => {
                      const mode = ThermostatMode.safeParse(value);
                      if (!mode.success) {
                        return;
                      }

                      onModeChange(mode.data);
                    }}
                    disabled={readOnly}
                  >
                    <SelectTrigger id="mode" className="h-8 w-32">
                      <SelectValue placeholder="Select mode" />
                    </SelectTrigger>
                    <SelectContent>
                      {allowedModes.map((mode) => (
                        <SelectItem key={mode} value={mode}>
                          {mode.charAt(0).toUpperCase() + mode.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex flex-row gap-2 items-center">
                  <Label
                    htmlFor="fanSpeed"
                    className="text-xs text-gray-500 flex-grow"
                  >
                    Fan Speed
                  </Label>
                  <Select
                    value={action.fanSpeed}
                    onValueChange={(value) => {
                      const fanSpeed = ThermostatFanSpeed.safeParse(value);
                      if (!fanSpeed.success) {
                        return;
                      }

                      onFanSpeedChange(fanSpeed.data);
                    }}
                    disabled={readOnly}
                  >
                    <SelectTrigger id="fanSpeed" className="h-8 w-32">
                      <SelectValue placeholder="Select fan speed" />
                    </SelectTrigger>
                    <SelectContent>
                      {allowedFanSpeeds.map((speed) => (
                        <SelectItem key={speed} value={speed}>
                          {speed.charAt(0).toUpperCase() + speed.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            );
          },
        )
        .exhaustive()}
    </SettingsRow>
  );
}

function DeviceSelectItem({
  deviceData,
}: {
  deviceData: ControllableNoHybrid;
}) {
  const IconComponent = deviceData.icon ? DeviceIcons[deviceData.icon] : null;
  const deviceType = defaultNameForNodeType(deviceData.type);

  return (
    <SelectItem
      key={deviceData.id}
      value={deviceData.id}
      className="flex flex-row w-full items-center "
    >
      <div className="flex flex-row items-center gap-1">
        {IconComponent && <IconComponent className="size-4" />}
        <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
          <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
            {deviceType}
          </div>
          <div className="truncate mb-0 text-[10px] font-semibold">
            {deviceData.name}
          </div>
        </div>
      </div>
    </SelectItem>
  );
}

// function SomfyShadesSelectItem({
//   device,
// }: {
//   device: Extract<DeviceWithSectionNode, { type: "somfyShades" }>;
// }) {
//   const { yDoc, nodes } = useReactFlowContext();

//   const baseStationNodes = filterNodes(nodes, "baseStationContainer");
//   const allShades = [];
//   for (const baseStationNode of baseStationNodes) {
//     const data = getData(yDoc, baseStationNode.data.dataId, "baseStation");
//     const somfyShades = data?.somfyShades || {};
//     for (const [shadeId, shade] of Object.entries(somfyShades)) {
//       allShades.push({
//         nodeId: baseStationNode.id,
//         ...shade,
//         id: shadeId,
//       });
//     }
//   }

//   const shades = allShades.find(
//     (shade) => device.viaId === shade.id && device.nodeId === shade.nodeId,
//   );
//   if (!shades) {
//     return null;
//   }
//   const IconComponent = shades.icon ? DeviceIcons[shades.icon] : null;
//   return (
//     <SelectItem
//       key={device.id}
//       value={device.id}
//       className="flex flex-row w-full items-center "
//     >
//       <div className="flex flex-row items-center gap-1">
//         {IconComponent && <IconComponent className="size-4" />}
//         <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
//           <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
//             {device.sectionNode.data.title}
//           </div>
//           <div className="truncate mb-0 text-[10px] font-semibold">
//             {shades.name}
//           </div>
//         </div>
//       </div>
//     </SelectItem>
//   );
// }

function DeleteButton({ onDelete }: { onDelete: () => void }) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="size-10 flex-shrink-0"
          onClick={onDelete}
        >
          <Trash2Icon className="size-4" />
        </Button>
      </TooltipTrigger>
      <TooltipContent>Delete</TooltipContent>
    </Tooltip>
  );
}
