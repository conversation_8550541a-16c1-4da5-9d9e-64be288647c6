import { useData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/layout-graph/ReactFlowContext";
import { ActionSettingsWithHeader } from "@/components/layout-graph/settings/ActionSettings";
import { LampPopover } from "@/components/layout-graph/settings/LampPopover";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "@/components/layout-graph/settings/SettingsGroup";
import { SettingsInput } from "@/components/layout-graph/settings/SettingsInput";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { TooltipDropdownMenu } from "@/components/ui/tooltip-dropdown-menu";
import { clamp } from "@/lib/math";
import {
  DoorSensorAction,
  DoorSensorViaId,
  LayoutContainerNode,
  randomId,
} from "@somo/shared";
import { useConnectableControllables } from "../useConnectableDevices";

import { CircleAlertIcon, PlusIcon } from "lucide-react";
import { DEFAULT_DIM_SPEED } from "../dimSpeed";

// Updated interface for the additional buttons
interface AdditionalButtonRowProps {
  id: DoorSensorViaId;
  label: string;
  button: DoorSensorAction;
  node: LayoutContainerNode;
}

function AdditionalButtonRow({
  id,
  label,
  button,
  node,
}: AdditionalButtonRowProps) {
  const { readOnly } = useReactFlowContext();
  const { data, updateNestedData, deleteNestedData } = useData(
    node.data.dataId,
    "doorSensor",
  );

  const actions = Object.values(button.onUpClick ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );
  const deviceActions = actions.filter(
    (action) => action.type === "lighting" || !("type" in action),
  );
  const thermostatActions = actions.filter(
    (action) => action.type === "thermostat",
  );

  const connectableDevices = useConnectableControllables({
    sourceType: "doorSensor",
    dataId: node.data.dataId,
    actions: deviceActions,
  }).filter(
    (device) =>
      device.type !== "roomThermostatControllable" && device.type !== "irHvac",
  );

  const connectableThermostats = useConnectableControllables({
    sourceType: "doorSensor",
    dataId: node.data.dataId,
    actions: thermostatActions,
  }).filter((device) => device.type === "roomThermostatControllable");

  const connectableIrControllers = useConnectableControllables({
    sourceType: "doorSensor",
    dataId: node.data.dataId,
    actions: thermostatActions,
  }).filter((device) => device.type === "irHvac");

  const firstUnusedDeviceId = connectableDevices.find((d) => !d.isUsed)?.id;
  const firstUnusedThermostatId = connectableThermostats.find(
    (d) => !d.isUsed,
  )?.id;
  const firstUnusedIrControllerId = connectableIrControllers.find(
    (d) => !d.isUsed,
  )?.id;
  const firstUnusedHvacId =
    firstUnusedThermostatId || firstUnusedIrControllerId;

  if (!data) {
    return <></>;
  }

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center justify-between">
        <Label className="text-gray-700">{label}</Label>
        <div className="flex flex-row items-center gap-1 w-[90px] flex-shrink-0">
          <Checkbox
            id={`${id}-enabled`}
            checked={button.enabled}
            onCheckedChange={() =>
              updateNestedData(`${id}.enabled`, !button.enabled)
            }
            disabled={readOnly}
          />
          <Label
            htmlFor={`${id}-enabled`}
            className="text-gray-500 text-xs font-semibold truncate"
          >
            Enabled
          </Label>
        </div>
      </div>

      {button.enabled && (
        <>
          <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
            <LampPopover
              activeIconKey={button.icon}
              onIconClick={(iconKey) => updateNestedData(`${id}.icon`, iconKey)}
              disabled={readOnly}
            />

            <SettingsInput
              className="text-sm font-normal h-10"
              value={button.name}
              onEndEdit={(name) => updateNestedData(`${id}.name`, name)}
              disabled={readOnly}
            />
            <div className="flex flex-row items-center gap-1 ml-3 w-[90px] flex-shrink-0">
              <Checkbox
                id={`${id}-showLabel`}
                checked={button.showLabel}
                onCheckedChange={(checked) =>
                  updateNestedData(`${id}.showLabel`, Boolean(checked))
                }
                disabled={readOnly}
              />
              <Label
                htmlFor={`${id}-showLabel`}
                className="text-gray-500 text-xs font-semibold truncate"
              >
                Show label
              </Label>
            </div>
          </div>
          <SettingsRow className="flex flex-row justify-between items-center gap-2 pr-2.5 mt-2">
            <SettingsInput
              className="w-auto"
              inputClassName="w-[80px] text-center"
              label="Delay (sec)"
              value={button.offDelay.toString()}
              disabled={readOnly}
              onEndEdit={(value) => {
                let numValue = parseInt(value, 10);
                if (isNaN(numValue)) {
                  numValue = 0;
                }
                // 1800 = 30 minutes seems a reasonable max value
                const newValue = clamp(numValue, { min: 0, max: 1_800 });
                updateNestedData(`${id}.offDelay`, newValue);
              }}
            />
          </SettingsRow>

          {/* Device Control Actions */}
          {deviceActions.length > 0 && (
            <ActionSettingsWithHeader
              type="lighting"
              label="Device Control"
              node={node}
              triggerId={id}
              devices={connectableDevices}
              actions={deviceActions}
              actionType="onUpClick"
              onDelete={(actionId) =>
                deleteNestedData(`${id}.onUpClick.${actionId}`)
              }
              onDeviceChange={(actionId, deviceId) => {
                updateNestedData(
                  `${id}.onUpClick.${actionId}.deviceId`,
                  deviceId,
                );
              }}
              onDelayChange={(actionId, value) => {
                // @ts-expect-error - Can't infer value type because of UnifiedActionSettings
                updateNestedData(`${id}.onUpClick.${actionId}.delay`, value);
              }}
              onDimSpeedChange={(actionId, value) => {
                // @ts-expect-error - Can't infer value type because of UnifiedActionSettings
                updateNestedData(`${id}.onUpClick.${actionId}.dimSpeed`, value);
              }}
              onOnValueChange={(actionId, value) => {
                // @ts-expect-error - Can't infer value type because of UnifiedActionSettings
                updateNestedData(`${id}.onUpClick.${actionId}.onValue`, value);
              }}
              onOffValueChange={(actionId, value) => {
                // @ts-expect-error - Can't infer value type because of UnifiedActionSettings
                updateNestedData(`${id}.onUpClick.${actionId}.offValue`, value);
              }}
              onTargetValueChange={(actionId, value) => {
                updateNestedData(
                  `${id}.onUpClick.${actionId}.targetValue`,
                  // @ts-expect-error - Can't infer value type because of UnifiedActionSettings
                  value,
                );
              }}
              onAddAction={(deviceId) => {
                const actionId = `device-control-${randomId()}`;
                const mapSize = Object.values(data[id].onUpClick).length;
                updateNestedData(`${id}.onUpClick.${actionId}`, {
                  id: actionId,
                  deviceId,
                  sortIndex: mapSize,
                  type: "lighting",
                  dimSpeed: DEFAULT_DIM_SPEED.click,
                  targetValue: 100,
                  onValue: 100,
                  offValue: 0,
                  delay: 0,
                });
              }}
              missingDeviceText="Create a device first"
            />
          )}

          {/* HVAC Control Actions */}
          {thermostatActions.length > 0 && (
            <ActionSettingsWithHeader
              type="thermostat"
              label="HVAC Control"
              node={node}
              triggerId={id}
              devices={[...connectableThermostats, ...connectableIrControllers]}
              actions={thermostatActions}
              actionType="onUpClick"
              onDelete={(actionId) =>
                deleteNestedData(`${id}.onUpClick.${actionId}`)
              }
              onDeviceChange={(actionId, deviceId) => {
                updateNestedData(
                  `${id}.onUpClick.${actionId}.deviceId`,
                  deviceId,
                );
              }}
              onSetPointChange={(actionId, value) => {
                // @ts-expect-error - Can't infer value type because of UnifiedActionSettings
                updateNestedData(`${id}.onUpClick.${actionId}.setpoint`, value);
              }}
              onModeChange={(actionId, value) => {
                // @ts-expect-error - Can't infer value type because of UnifiedActionSettings
                updateNestedData(`${id}.onUpClick.${actionId}.mode`, value);
              }}
              onFanSpeedChange={(actionId, value) => {
                // @ts-expect-error - Can't infer value type because of UnifiedActionSettings
                updateNestedData(`${id}.onUpClick.${actionId}.fanSpeed`, value);
              }}
              onAddAction={(deviceId) => {
                const newActionId = randomId();
                const mapSize = Object.values(data[id].onUpClick).length;
                updateNestedData(`${id}.onUpClick.${newActionId}`, {
                  id: newActionId,
                  deviceId,
                  sortIndex: mapSize,
                  setpoint: 22,
                  mode: "auto",
                  fanSpeed: "auto",
                  type: "thermostat",
                });
              }}
              missingDeviceText="Create an HVAC device first"
            />
          )}

          {/* Show add buttons when no actions exist */}
          {actions.length === 0 && (
            <SettingsRow className="flex flex-row justify-between items-center gap-2 pl-1 pr-2.5 mt-2">
              <SettingsLabel>Execute</SettingsLabel>
              {!readOnly && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <TooltipDropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="size-6 data-[state=open]:bg-gray-100"
                        >
                          <PlusIcon className="size-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="mr-5">
                        <DropdownMenuLabel>Add action</DropdownMenuLabel>
                        {firstUnusedDeviceId ? (
                          <DropdownMenuItem
                            onClick={() => {
                              const actionId = `device-control-${randomId()}`;
                              const mapSize = Object.values(
                                data[id].onUpClick,
                              ).length;
                              updateNestedData(`${id}.onUpClick.${actionId}`, {
                                id: actionId,
                                deviceId: firstUnusedDeviceId,
                                sortIndex: mapSize,
                                type: "lighting",
                                dimSpeed: DEFAULT_DIM_SPEED.click,
                                targetValue: 100,
                                onValue: 100,
                                offValue: 0,
                                delay: 0,
                              });
                            }}
                          >
                            Device Control
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem disabled>
                            <div className="flex items-center text-red-600">
                              <CircleAlertIcon className="mr-2 size-4" />
                              Create a device first
                            </div>
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />

                        <DropdownMenuLabel>HVAC Control</DropdownMenuLabel>
                        {firstUnusedHvacId ? (
                          <DropdownMenuItem
                            onClick={() => {
                              if (!firstUnusedHvacId) {
                                console.warn("No HVAC device found");
                                return;
                              }

                              const newActionId = randomId();
                              const mapSize = Object.values(
                                data[id].onUpClick,
                              ).length;
                              updateNestedData(
                                `${id}.onUpClick.${newActionId}`,
                                {
                                  id: newActionId,
                                  deviceId: firstUnusedHvacId,
                                  sortIndex: mapSize,
                                  setpoint: 22,
                                  mode: "auto",
                                  fanSpeed: "auto",
                                  type: "thermostat",
                                },
                              );
                            }}
                          >
                            HVAC Control
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem disabled>
                            <div className="flex items-center text-red-600">
                              <CircleAlertIcon className="mr-2 size-4" />
                              Create an HVAC device first
                            </div>
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </TooltipDropdownMenu>
                  </TooltipTrigger>
                  <TooltipContent>Add action</TooltipContent>
                </Tooltip>
              )}
            </SettingsRow>
          )}

          {actions.length === 0 && (
            <div className="text-gray-500 text-xs px-1">
              No actions defined.
            </div>
          )}
        </>
      )}
    </div>
  );
}

export function DoorSensorSettings({ node }: { node: LayoutContainerNode }) {
  const { readOnly } = useReactFlowContext();
  const { data, updateNestedData } = useData(node.data.dataId, "doorSensor");

  if (!data) {
    return <></>;
  }

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">
              ID: {node.data.dataId}
            </div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={data.name}
              onEndEdit={(value) => updateNestedData("name", value)}
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>
      <SettingsGroup className="mb-4">
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Sensor Actions</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow>
            <AdditionalButtonRow
              id="onOpen"
              label="On Open"
              button={data.onOpen}
              node={node}
            />
          </SettingsRow>

          <SettingsRow className="mt-4">
            <AdditionalButtonRow
              id="onClose"
              label="On Close"
              button={data.onClose}
              node={node}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}
